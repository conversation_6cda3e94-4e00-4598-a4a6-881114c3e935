import sqlite3
import json
from datetime import datetime, timedelta
from http.server import HTTPServer, SimpleHTTPRequestHandler
import urllib.parse
import os
import webbrowser
import threading
import time

class DashboardHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.serve_dashboard()
        elif self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            super().do_GET()
    
    def serve_dashboard(self):
        """Serve the main dashboard HTML with real data integration"""
        dashboard_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Govee Temperature Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        select, input, button {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-weight: 500;
        }

        .freezer { border-left: 4px solid #3b82f6; }
        .fridge { border-left: 4px solid #10b981; }
        .warning { border-left: 4px solid #f59e0b; }
        .danger { border-left: 4px solid #ef4444; }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-container, .data-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chart-container h3, .data-container h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #555;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .status-online {
            color: #10b981;
            font-weight: 600;
        }

        .status-offline {
            color: #ef4444;
            font-weight: 600;
        }

        .temp-cold {
            color: #3b82f6;
            font-weight: 600;
        }

        .temp-normal {
            color: #10b981;
            font-weight: 600;
        }

        .temp-warm {
            color: #f59e0b;
            font-weight: 600;
        }

        .temp-hot {
            color: #ef4444;
            font-weight: 600;
        }

        .alerts {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .alert-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-warning {
            background: #fef3c7;
            border-color: #f59e0b;
        }

        .alert-danger {
            background: #fee2e2;
            border-color: #ef4444;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .control-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌡️ Govee Temperature Dashboard</h1>
            <p>Professional Temperature Monitoring System</p>
        </div>

        <div class="controls">
            <div class="control-row">
                <div class="control-group">
                    <label for="viewType">View Type</label>
                    <select id="viewType">
                        <option value="current">Current Status</option>
                        <option value="daily">Daily Averages</option>
                        <option value="weekly">Weekly Summary</option>
                        <option value="alerts">Temperature Alerts</option>
                        <option value="trends">Temperature Trends</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="timeRange">Time Range</label>
                    <select id="timeRange">
                        <option value="1">Last 24 Hours</option>
                        <option value="7" selected>Last 7 Days</option>
                        <option value="30">Last 30 Days</option>
                        <option value="90">Last 90 Days</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="deviceFilter">Device Filter</label>
                    <select id="deviceFilter">
                        <option value="all">All Devices</option>
                        <option value="Congélateur">Freezers Only</option>
                        <option value="Frigidaire">Fridges Only</option>
                    </select>
                </div>
                
                <button onclick="loadData()">🔄 Refresh Data</button>
                <button onclick="exportData()">📊 Export CSV</button>
            </div>
        </div>

        <div class="stats-grid" id="statsGrid">
            <!-- Stats will be populated here -->
        </div>

        <div class="main-content">
            <div class="chart-container">
                <h3>📈 Temperature Trends</h3>
                <canvas id="temperatureChart" width="400" height="200"></canvas>
            </div>
            
            <div class="data-container">
                <h3>📋 Recent Readings</h3>
                <div id="dataTable">
                    <div class="loading">Loading data...</div>
                </div>
            </div>
        </div>

        <div class="alerts">
            <h3>🚨 Temperature Alerts</h3>
            <div id="alertsContainer">
                <div class="loading">Checking for alerts...</div>
            </div>
        </div>
    </div>

    <script>
        let temperatureChart = null;

        function getTemperatureClass(temp, appliance) {
            if (appliance && appliance.includes('Congélateur')) {
                if (temp > -15) return 'temp-hot';
                if (temp > -18) return 'temp-warm';
                return 'temp-cold';
            } else {
                if (temp > 8) return 'temp-hot';
                if (temp > 5) return 'temp-warm';
                return 'temp-normal';
            }
        }

        function generateStats(data) {
            const freezers = data.filter(d => d.device && d.device.includes('Congélateur'));
            const fridges = data.filter(d => d.device && d.device.includes('Frigidaire'));
            const online = data.filter(d => d.online).length;
            const alerts = data.filter(d => 
                (d.device && d.device.includes('Congélateur') && d.temp > -15) ||
                (d.device && d.device.includes('Frigidaire') && d.temp > 8) ||
                !d.online
            ).length;

            return [
                { icon: '🧊', value: freezers.length, label: 'Freezers', class: 'freezer' },
                { icon: '❄️', value: fridges.length, label: 'Fridges', class: 'fridge' },
                { icon: '🟢', value: online, label: 'Online', class: 'fridge' },
                { icon: '🚨', value: alerts, label: 'Alerts', class: alerts > 0 ? 'danger' : 'fridge' }
            ];
        }

        function renderStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = stats.map(stat => `
                <div class="stat-card ${stat.class}">
                    <div class="stat-icon">${stat.icon}</div>
                    <div class="stat-value">${stat.value}</div>
                    <div class="stat-label">${stat.label}</div>
                </div>
            `).join('');
        }

        function renderDataTable(data) {
            const container = document.getElementById('dataTable');
            
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="no-data">No data available</div>';
                return;
            }

            const table = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Device</th>
                            <th>Temperature</th>
                            <th>Humidity</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map(row => `
                            <tr>
                                <td>${row.device || 'Unknown'}</td>
                                <td class="${getTemperatureClass(row.temp, row.device)}">${row.temp ? row.temp.toFixed(1) : 'N/A'}°C</td>
                                <td>${row.humidity ? row.humidity + '%' : 'N/A'}</td>
                                <td class="${row.online ? 'status-online' : 'status-offline'}">
                                    ${row.online ? '🟢 Online' : '🔴 Offline'}
                                </td>
                                <td>${row.time ? new Date(row.time).toLocaleString() : 'N/A'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }

        function renderChart(data) {
            const ctx = document.getElementById('temperatureChart').getContext('2d');

            if (temperatureChart) {
                temperatureChart.destroy();
            }

            if (!data || data.length === 0) {
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('No trend data available', ctx.canvas.width/2, ctx.canvas.height/2);
                return;
            }

            // Get all unique dates and sort them
            const labels = [...new Set(data.map(d => d.date))].sort();

            // Group data by device
            const deviceData = {};
            data.forEach(d => {
                if (!deviceData[d.device]) {
                    deviceData[d.device] = {};
                }
                deviceData[d.device][d.date] = d.avg_temp;
            });

            const colors = ['#667eea', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
            const datasets = Object.keys(deviceData).map((device, index) => {
                // Create data array aligned with labels, using null for missing dates
                const deviceTemps = labels.map(date => deviceData[device][date] || null);

                return {
                    label: device,
                    data: deviceTemps,
                    borderColor: colors[index % colors.length],
                    backgroundColor: colors[index % colors.length] + '20',
                    tension: 0.4,
                    fill: false,
                    spanGaps: true  // This connects points across null values
                };
            });

            temperatureChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels.map(date => new Date(date).toLocaleDateString()),
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });
        }

        function renderAlerts(data) {
            const container = document.getElementById('alertsContainer');
            const alerts = data.filter(d => 
                (d.device && d.device.includes('Congélateur') && d.temp > -15) ||
                (d.device && d.device.includes('Frigidaire') && d.temp > 8) ||
                !d.online
            );

            if (alerts.length === 0) {
                container.innerHTML = '<div class="no-data">✅ All systems normal - no alerts</div>';
                return;
            }

            container.innerHTML = alerts.map(alert => {
                let alertClass = 'alert-warning';
                let message = '';
                
                if (!alert.online) {
                    alertClass = 'alert-danger';
                    message = `🔴 ${alert.device} is OFFLINE`;
                } else if (alert.device && alert.device.includes('Congélateur') && alert.temp > -15) {
                    alertClass = 'alert-danger';
                    message = `🚨 ${alert.device} temperature too high: ${alert.temp.toFixed(1)}°C (should be below -15°C)`;
                } else if (alert.device && alert.device.includes('Frigidaire') && alert.temp > 8) {
                    alertClass = 'alert-warning';
                    message = `⚠️ ${alert.device} temperature elevated: ${alert.temp.toFixed(1)}°C (should be below 8°C)`;
                }

                return `<div class="alert-item ${alertClass}">${message}</div>`;
            }).join('');
        }

        async function loadData() {
            try {
                // Load current data
                const currentResponse = await fetch('/api/current');
                const currentData = await currentResponse.json();
                
                // Load trends data
                const timeRange = document.getElementById('timeRange').value;
                const trendsResponse = await fetch(`/api/trends?days=${timeRange}`);
                const trendsData = await trendsResponse.json();

                const stats = generateStats(currentData);
                renderStats(stats);
                renderDataTable(currentData);
                renderChart(trendsData);
                renderAlerts(currentData);
            } catch (error) {
                console.error('Error loading data:', error);
                document.getElementById('dataTable').innerHTML = '<div class="no-data">Error loading data</div>';
                document.getElementById('alertsContainer').innerHTML = '<div class="no-data">Error loading alerts</div>';
            }
        }

        async function exportData() {
            try {
                const response = await fetch('/api/export');
                const data = await response.json();
                
                const csv = [
                    ['Device', 'Location', 'Temperature (°C)', 'Humidity (%)', 'Online', 'Timestamp'],
                    ...data.map(row => [row.device, row.location, row.temp, row.humidity, row.online, row.time])
                ].map(row => row.join(',')).join('\\n');

                const blob = new Blob([csv], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `govee_temperatures_${new Date().toISOString().split('T')[0]}.csv`;
                a.click();
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error('Error exporting data:', error);
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            
            // Auto-refresh every 5 minutes
            setInterval(loadData, 5 * 60 * 1000);
        });

        // Handle filter changes
        document.getElementById('viewType').addEventListener('change', loadData);
        document.getElementById('timeRange').addEventListener('change', loadData);
        document.getElementById('deviceFilter').addEventListener('change', loadData);
    </script>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(dashboard_html.encode())

    def handle_api_request(self):
        """Handle API requests for data"""
        try:
            if self.path == '/api/current':
                data = get_current_readings()
            elif self.path.startswith('/api/trends'):
                # Parse query parameters
                query = urllib.parse.urlparse(self.path).query
                params = urllib.parse.parse_qs(query)
                days = int(params.get('days', [7])[0])
                data = get_trends_data(days)
            elif self.path == '/api/export':
                data = get_export_data()
            else:
                self.send_response(404)
                self.end_headers()
                return
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(data).encode())
            
        except Exception as e:
            print(f"API Error: {e}")
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode())

def get_current_readings():
    """Get the most recent temperature reading for each device"""
    try:
        conn = sqlite3.connect('govee_temperatures.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                device_name,
                appliance_type,
                location,
                temperature_c,
                humidity,
                online,
                recorded_at
            FROM temperature_readings t1
            WHERE recorded_at = (
                SELECT MAX(recorded_at) 
                FROM temperature_readings t2 
                WHERE t1.device_id = t2.device_id
            )
            ORDER BY appliance_type, location
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        # Convert to format expected by frontend
        data = []
        for row in results:
            device_name, appliance_type, location, temp_c, humidity, online, recorded_at = row
            data.append({
                'device': device_name,
                'location': location,
                'temp': temp_c if temp_c is not None else 0,
                'humidity': humidity if humidity is not None else 0,
                'online': bool(online),
                'time': recorded_at
            })
        
        return data
        
    except Exception as e:
        print(f"Database error in get_current_readings: {e}")
        return []

def get_trends_data(days=7):
    """Get trend data for charts"""
    try:
        conn = sqlite3.connect('govee_temperatures.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                DATE(recorded_at) as date,
                device_name,
                ROUND(AVG(temperature_c), 1) as avg_temp
            FROM temperature_readings 
            WHERE recorded_at >= datetime('now', '-{} days')
            AND temperature_c IS NOT NULL
            GROUP BY DATE(recorded_at), device_id
            ORDER BY date DESC, device_name
        '''.format(days))
        
        results = cursor.fetchall()
        conn.close()
        
        # Convert to format expected by frontend
        data = []
        for row in results:
            date, device_name, avg_temp = row
            data.append({
                'date': date,
                'device': device_name,
                'avg_temp': avg_temp if avg_temp is not None else 0
            })
        
        return data
        
    except Exception as e:
        print(f"Database error in get_trends_data: {e}")
        return []

def get_export_data():
    """Get data for CSV export"""
    try:
        conn = sqlite3.connect('govee_temperatures.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                device_name,
                location,
                temperature_c,
                humidity,
                online,
                recorded_at
            FROM temperature_readings
            WHERE recorded_at >= datetime('now', '-30 days')
            ORDER BY recorded_at DESC
            LIMIT 1000
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        # Convert to format expected by frontend
        data = []
        for row in results:
            device_name, location, temp_c, humidity, online, recorded_at = row
            data.append({
                'device': device_name,
                'location': location if location else '',
                'temp': temp_c if temp_c is not None else 0,
                'humidity': humidity if humidity is not None else 0,
                'online': 'Yes' if online else 'No',
                'time': recorded_at
            })
        
        return data
        
    except Exception as e:
        print(f"Database error in get_export_data: {e}")
        return []

def check_database():
    """Check if database exists and has data"""
    if not os.path.exists('govee_temperatures.db'):
        print("❌ Database file 'govee_temperatures.db' not found!")
        print("Please run the data collection script first to create the database.")
        return False
    
    try:
        conn = sqlite3.connect('govee_temperatures.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM temperature_readings")
        count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MAX(recorded_at) FROM temperature_readings")
        latest = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ Database found with {count} temperature readings")
        if latest:
            print(f"   Latest reading: {latest}")
        
        return count > 0
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def start_server(port=8080):
    """Start the web server"""
    try:
        server_address = ('', port)
        httpd = HTTPServer(server_address, DashboardHandler)
        
        print(f"🌐 Starting Govee Temperature Dashboard Server...")
        print(f"🔗 Server running at: http://localhost:{port}")
        print(f"📊 Dashboard URL: http://localhost:{port}")
        print(f"🛑 Press Ctrl+C to stop the server")
        
        # Try to open browser automatically
        def open_browser():
            time.sleep(1)  # Wait a moment for server to start
            try:
                webbrowser.open(f'http://localhost:{port}')
                print(f"🌐 Opening dashboard in your default browser...")
            except:
                pass
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print(f"\n🛑 Server stopped by user")
        httpd.shutdown()
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == "__main__":
    print("🌡️ Govee Temperature Dashboard Server")
    print("=" * 50)
    
    # Check database
    if not check_database():
        print("\n⚠️  Warning: No data found in database")
        print("The dashboard will work but show no data until you run the data collection script.")
        print()
    
    # Start server
    try:
        port = 8080
        print(f"\n🚀 Starting server on port {port}...")
        start_server(port)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        print("Try a different port or check if port 8080 is already in use.")