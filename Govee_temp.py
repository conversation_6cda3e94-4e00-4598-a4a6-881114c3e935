import requests
import sqlite3
import ssl
import uuid
import time
from datetime import datetime

print(ssl.OPENSSL_VERSION)

# Initialize SQLite database
def init_database():
    """Create database and tables if they don't exist"""
    conn = sqlite3.connect('govee_temperatures.db')
    cursor = conn.cursor()
    
    # Create devices table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS devices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            device_id TEXT UNIQUE NOT NULL,
            device_name TEXT NOT NULL,
            model TEXT,
            appliance_type TEXT,
            location TEXT,
            device_number TEXT,
            last_updated DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create temperature readings table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS temperature_readings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            device_id TEXT NOT NULL,
            device_name TEXT NOT NULL,
            appliance_type TEXT,
            location TEXT,
            temperature_c REAL,
            humidity REAL,
            online BOOLEAN,
            recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (device_id) REFERENCES devices (device_id)
        )
    ''')
    
    # Create index for faster queries
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_readings_date 
        ON temperature_readings (recorded_at)
    ''')
    
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_readings_device 
        ON temperature_readings (device_id, recorded_at)
    ''')
    
    conn.commit()
    conn.close()

def update_device(device_data):
    """Insert or update device information"""
    conn = sqlite3.connect('govee_temperatures.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT OR REPLACE INTO devices 
        (device_id, device_name, model, appliance_type, location, device_number, last_updated)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (
        device_data['device_id'],
        device_data['device_name'],
        device_data['model'],
        device_data['appliance_type'],
        device_data['location'],
        device_data['device_number'],
        device_data['last_updated']
    ))
    
    conn.commit()
    conn.close()

def insert_temperature_reading(temp_data):
    """Insert a new temperature reading"""
    conn = sqlite3.connect('govee_temperatures.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO temperature_readings 
        (device_id, device_name, appliance_type, location, temperature_c, humidity, online, recorded_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        temp_data['device_id'],
        temp_data['device'],
        temp_data['appliance'],
        temp_data['location'],
        temp_data['temp_c'],
        temp_data['humidity'],
        temp_data['online'] == 'Yes',
        temp_data['timestamp']
    ))
    
    conn.commit()
    conn.close()

def get_latest_readings():
    """Get the most recent temperature reading for each device"""
    conn = sqlite3.connect('govee_temperatures.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT device_name, appliance_type, location, temperature_c, humidity, online, recorded_at
        FROM temperature_readings t1
        WHERE recorded_at = (
            SELECT MAX(recorded_at) 
            FROM temperature_readings t2 
            WHERE t1.device_id = t2.device_id
        )
        ORDER BY appliance_type, location
    ''')
    
    results = cursor.fetchall()
    conn.close()
    return results

def get_daily_average(days=7):
    """Get daily averages for the last N days"""
    conn = sqlite3.connect('govee_temperatures.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT 
            DATE(recorded_at) as date,
            device_name,
            appliance_type,
            location,
            ROUND(AVG(temperature_c), 1) as avg_temp,
            ROUND(AVG(humidity), 1) as avg_humidity,
            COUNT(*) as readings_count
        FROM temperature_readings 
        WHERE recorded_at >= datetime('now', '-{} days')
        GROUP BY DATE(recorded_at), device_id
        ORDER BY date DESC, appliance_type, location
    '''.format(days))
    
    results = cursor.fetchall()
    conn.close()
    return results

# Initialize database
init_database()

url = "https://openapi.api.govee.com/router/api/v1/user/devices"
headers = {
    "Content-Type": "application/json",
    "Govee-API-Key": "e65a8791-f3a5-4a55-8412-2e1b698bbb4f"  # Replace with your actual API key
}

response = requests.get(url, headers=headers)
result = response.json()

print(f"API Response Status: {response.status_code}")

if response.status_code == 200:
    devices = result.get("data", [])
    print(f"\nFound {len(devices)} devices:\n")
    
    readings_added = 0
    
    for i, device in enumerate(devices):
        name = device.get("deviceName", "Unnamed")
        model = device.get("sku")        
        device_id = device.get("device")
        device_type = device.get("type", "Unknown")

        print(f"{i}. {name} — ID: {device_id[-8:]} — Model: {model} — Type: {device_type}")

        # Parse location and appliance type from device name
        location = ""
        appliance_type = ""
        device_number = ""
        
        if "#" in name:
            parts = name.split("#")
            appliance_type = parts[0]  # Congélateur or Frigidaire
            if len(parts) > 1:
                location_part = parts[1].strip()
                location_words = location_part.split()
                if len(location_words) >= 2:
                    device_number = location_words[0]
                    location = " ".join(location_words[1:])
                else:
                    location = location_part
        
        # Get device state
        state_url = "https://openapi.api.govee.com/router/api/v1/device/state"
        request_id = str(uuid.uuid4())
        
        payload = {
            "requestId": request_id,
            "payload": {
                "sku": model,
                "device": device_id
            }
        }

        try:
            state_response = requests.post(state_url, headers=headers, json=payload, timeout=10)
            
            if state_response.status_code == 200:
                state_result = state_response.json()
                payload_data = state_result.get("payload", {})
                
                # Extract data
                online_status = False
                temperature_f = None
                temperature_c = None
                humidity = None
                
                capabilities = payload_data.get("capabilities", [])
                
                for cap in capabilities:
                    cap_type = cap.get("type", "")
                    instance = cap.get("instance", "")
                    state = cap.get("state", {})
                    
                    if instance == "online":
                        online_status = state.get("value", False)
                    elif "temperature" in instance.lower():
                        temperature_f = state.get("value")
                        if temperature_f is not None:
                            temperature_c = (temperature_f - 32) * 5/9
                    elif "humidity" in instance.lower():
                        humidity = state.get("value")
                
                # Create device record
                device_record = {
                    'device_id': device_id[-8:],
                    'device_name': f"{appliance_type} {location}",
                    'model': model,
                    'appliance_type': appliance_type,
                    'location': location,
                    'device_number': device_number,
                    'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # Update device in database
                update_device(device_record)
                
                # Create and store temperature record
                if temperature_c is not None:
                    temp_record = {
                        'device_id': device_id[-8:],
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'device': f"{appliance_type} {location}",
                        'appliance': appliance_type,
                        'location': location,
                        'temp_c': round(temperature_c, 1),
                        'humidity': round(humidity, 1) if humidity is not None else None,
                        'online': 'Yes' if online_status else 'No'
                    }
                    
                    # Insert temperature reading
                    insert_temperature_reading(temp_record)
                    readings_added += 1
                
                # Display results
                print(f"   📡 Online: {online_status}")
                if temperature_c is not None:
                    print(f"   🌡️ Temperature: {temperature_c:.1f}°C")
                if humidity is not None:
                    print(f"   💧 Humidity: {humidity:.1f}%")
                    
            else:
                print(f"   ❌ Error {state_response.status_code}: {state_response.text}")
                
        except Exception as e:
            print(f"   ⚠️ Exception: {e}")
            
        time.sleep(1)  # Rate limiting
        print()

    # Display current readings from database
    print("=" * 80)
    print("CURRENT READINGS FROM DATABASE")
    print("=" * 80)
    
    latest_readings = get_latest_readings()
    
    freezers = [r for r in latest_readings if r[1] == 'Congélateur']
    fridges = [r for r in latest_readings if r[1] == 'Frigidaire']
    
    if freezers:
        print("\n🧊 FREEZERS (Congélateurs):")
        for reading in freezers:
            device_name, appliance_type, location, temp_c, humidity, online, recorded_at = reading
            status = "🟢 Online" if online else "🔴 Offline"
            print(f"   {location}: {temp_c:.1f}°C | {humidity}% | {status} | {recorded_at}")
    
    if fridges:
        print("\n❄️ FRIDGES (Frigidaires):")
        for reading in fridges:
            device_name, appliance_type, location, temp_c, humidity, online, recorded_at = reading
            status = "🟢 Online" if online else "🔴 Offline"
            print(f"   {location}: {temp_c:.1f}°C | {humidity}% | {status} | {recorded_at}")
    
    # Show daily averages for last 7 days
    print("\n" + "=" * 80)
    print("DAILY AVERAGES (Last 7 Days)")
    print("=" * 80)
    
    daily_averages = get_daily_average(7)
    current_date = None
    
    for avg in daily_averages:
        date, device_name, appliance_type, location, avg_temp, avg_humidity, count = avg
        if date != current_date:
            if current_date is not None:
                print()
            print(f"\n📅 {date}:")
            current_date = date
        
        emoji = "🧊" if appliance_type == "Congélateur" else "❄️"
        print(f"   {emoji} {location}: {avg_temp}°C avg | {avg_humidity}% avg | ({count} readings)")
    
    print(f"\n💾 Database updated successfully!")
    print(f"   - {readings_added} new temperature readings added")
    print(f"   - Database file: govee_temperatures.db")
    
    # Example queries you can run
    print(f"\n🔍 EXAMPLE SQL QUERIES:")
    print("   - SELECT * FROM temperature_readings WHERE DATE(recorded_at) = '2024-01-15';")
    print("   - SELECT location, AVG(temperature_c) FROM temperature_readings GROUP BY location;")
    print("   - SELECT * FROM temperature_readings WHERE temperature_c < -18 AND appliance_type = 'Congélateur';")

else:
    print("❌ Failed to get devices.")
    print(response.text)