<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Govee Temperature Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        select, input, button {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-weight: 500;
        }

        .freezer { border-left: 4px solid #3b82f6; }
        .fridge { border-left: 4px solid #10b981; }
        .warning { border-left: 4px solid #f59e0b; }
        .danger { border-left: 4px solid #ef4444; }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-container, .data-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chart-container h3, .data-container h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #555;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .status-online {
            color: #10b981;
            font-weight: 600;
        }

        .status-offline {
            color: #ef4444;
            font-weight: 600;
        }

        .temp-cold {
            color: #3b82f6;
            font-weight: 600;
        }

        .temp-normal {
            color: #10b981;
            font-weight: 600;
        }

        .temp-warm {
            color: #f59e0b;
            font-weight: 600;
        }

        .temp-hot {
            color: #ef4444;
            font-weight: 600;
        }

        .alerts {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .alert-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-warning {
            background: #fef3c7;
            border-color: #f59e0b;
        }

        .alert-danger {
            background: #fee2e2;
            border-color: #ef4444;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .control-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌡️ Govee Temperature Dashboard</h1>
            <p>Professional Temperature Monitoring System</p>
        </div>

        <div class="controls">
            <div class="control-row">
                <div class="control-group">
                    <label for="viewType">View Type</label>
                    <select id="viewType">
                        <option value="current">Current Status</option>
                        <option value="daily">Daily Averages</option>
                        <option value="weekly">Weekly Summary</option>
                        <option value="alerts">Temperature Alerts</option>
                        <option value="trends">Temperature Trends</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="timeRange">Time Range</label>
                    <select id="timeRange">
                        <option value="1">Last 24 Hours</option>
                        <option value="7" selected>Last 7 Days</option>
                        <option value="30">Last 30 Days</option>
                        <option value="90">Last 90 Days</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="deviceFilter">Device Filter</label>
                    <select id="deviceFilter">
                        <option value="all">All Devices</option>
                        <option value="Congélateur">Freezers Only</option>
                        <option value="Frigidaire">Fridges Only</option>
                    </select>
                </div>
                
                <button onclick="loadData()">🔄 Refresh Data</button>
                <button onclick="exportData()">📊 Export CSV</button>
            </div>
        </div>

        <div class="stats-grid" id="statsGrid">
            <!-- Stats will be populated here -->
        </div>

        <div class="main-content">
            <div class="chart-container">
                <h3>📈 Temperature Trends</h3>
                <canvas id="temperatureChart" width="400" height="200"></canvas>
            </div>
            
            <div class="data-container">
                <h3>📋 Recent Readings</h3>
                <div id="dataTable">
                    <div class="loading">Loading data...</div>
                </div>
            </div>
        </div>

        <div class="alerts">
            <h3>🚨 Temperature Alerts</h3>
            <div id="alertsContainer">
                <div class="loading">Checking for alerts...</div>
            </div>
        </div>
    </div>

    <script>
        // Simulated data - replace with actual SQLite data
        const mockData = {
            current: [
                { device: "Congélateur bar", location: "bar", temp: -18.5, humidity: 45, online: true, time: "2024-01-15 14:30:00" },
                { device: "Frigidaire cuisine", location: "cuisine", temp: 3.2, humidity: 78, online: true, time: "2024-01-15 14:30:00" },
                { device: "Congélateur terrasse", location: "terrasse", temp: -17.8, humidity: 42, online: true, time: "2024-01-15 14:30:00" },
                { device: "Frigidaire bar", location: "bar", temp: 4.1, humidity: 81, online: false, time: "2024-01-15 14:25:00" }
            ],
            trends: [
                { date: "2024-01-09", device: "Congélateur bar", avg_temp: -18.3 },
                { date: "2024-01-10", device: "Congélateur bar", avg_temp: -18.1 },
                { date: "2024-01-11", device: "Congélateur bar", avg_temp: -18.5 },
                { date: "2024-01-12", device: "Congélateur bar", avg_temp: -18.2 },
                { date: "2024-01-13", device: "Congélateur bar", avg_temp: -18.4 },
                { date: "2024-01-14", device: "Congélateur bar", avg_temp: -18.6 },
                { date: "2024-01-15", device: "Congélateur bar", avg_temp: -18.5 }
            ]
        };

        let temperatureChart = null;

        function getTemperatureClass(temp, appliance) {
            if (appliance.includes('Congélateur')) {
                if (temp > -15) return 'temp-hot';
                if (temp > -18) return 'temp-warm';
                return 'temp-cold';
            } else {
                if (temp > 8) return 'temp-hot';
                if (temp > 5) return 'temp-warm';
                return 'temp-normal';
            }
        }

        function generateStats(data) {
            const freezers = data.filter(d => d.device.includes('Congélateur'));
            const fridges = data.filter(d => d.device.includes('Frigidaire'));
            const online = data.filter(d => d.online).length;
            const alerts = data.filter(d => 
                (d.device.includes('Congélateur') && d.temp > -15) ||
                (d.device.includes('Frigidaire') && d.temp > 8)
            ).length;

            return [
                { icon: '🧊', value: freezers.length, label: 'Freezers', class: 'freezer' },
                { icon: '❄️', value: fridges.length, label: 'Fridges', class: 'fridge' },
                { icon: '🟢', value: online, label: 'Online', class: 'fridge' },
                { icon: '🚨', value: alerts, label: 'Alerts', class: alerts > 0 ? 'danger' : 'fridge' }
            ];
        }

        function renderStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = stats.map(stat => `
                <div class="stat-card ${stat.class}">
                    <div class="stat-icon">${stat.icon}</div>
                    <div class="stat-value">${stat.value}</div>
                    <div class="stat-label">${stat.label}</div>
                </div>
            `).join('');
        }

        function renderDataTable(data) {
            const container = document.getElementById('dataTable');
            
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="no-data">No data available</div>';
                return;
            }

            const table = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Device</th>
                            <th>Temperature</th>
                            <th>Humidity</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map(row => `
                            <tr>
                                <td>${row.device}</td>
                                <td class="${getTemperatureClass(row.temp, row.device)}">${row.temp.toFixed(1)}°C</td>
                                <td>${row.humidity}%</td>
                                <td class="${row.online ? 'status-online' : 'status-offline'}">
                                    ${row.online ? '🟢 Online' : '🔴 Offline'}
                                </td>
                                <td>${new Date(row.time).toLocaleString()}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }

        function renderChart(data) {
            const ctx = document.getElementById('temperatureChart').getContext('2d');
            
            if (temperatureChart) {
                temperatureChart.destroy();
            }

            const chartData = {
                labels: data.map(d => new Date(d.date).toLocaleDateString()),
                datasets: [{
                    label: 'Temperature (°C)',
                    data: data.map(d => d.avg_temp),
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            };

            temperatureChart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });
        }

        function renderAlerts(data) {
            const container = document.getElementById('alertsContainer');
            const alerts = data.filter(d => 
                (d.device.includes('Congélateur') && d.temp > -15) ||
                (d.device.includes('Frigidaire') && d.temp > 8) ||
                !d.online
            );

            if (alerts.length === 0) {
                container.innerHTML = '<div class="no-data">✅ All systems normal - no alerts</div>';
                return;
            }

            container.innerHTML = alerts.map(alert => {
                let alertClass = 'alert-warning';
                let message = '';
                
                if (!alert.online) {
                    alertClass = 'alert-danger';
                    message = `🔴 ${alert.device} is OFFLINE`;
                } else if (alert.device.includes('Congélateur') && alert.temp > -15) {
                    alertClass = 'alert-danger';
                    message = `🚨 ${alert.device} temperature too high: ${alert.temp.toFixed(1)}°C (should be below -15°C)`;
                } else if (alert.device.includes('Frigidaire') && alert.temp > 8) {
                    alertClass = 'alert-warning';
                    message = `⚠️ ${alert.device} temperature elevated: ${alert.temp.toFixed(1)}°C (should be below 8°C)`;
                }

                return `<div class="alert-item ${alertClass}">${message}</div>`;
            }).join('');
        }

        function loadData() {
            // In real implementation, this would query your SQLite database
            // For now, using mock data
            const currentData = mockData.current;
            const trendData = mockData.trends;

            const stats = generateStats(currentData);
            renderStats(stats);
            renderDataTable(currentData);
            renderChart(trendData);
            renderAlerts(currentData);
        }

        function exportData() {
            // Convert current data to CSV
            const data = mockData.current;
            const csv = [
                ['Device', 'Location', 'Temperature (°C)', 'Humidity (%)', 'Online', 'Timestamp'],
                ...data.map(row => [row.device, row.location, row.temp, row.humidity, row.online, row.time])
            ].map(row => row.join(',')).join('\n');

            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `govee_temperatures_${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            
            // Auto-refresh every 5 minutes
            setInterval(loadData, 5 * 60 * 1000);
        });

        // Handle filter changes
        document.getElementById('viewType').addEventListener('change', loadData);
        document.getElementById('timeRange').addEventListener('change', loadData);
        document.getElementById('deviceFilter').addEventListener('change', loadData);
    </script>
</body>
</html>